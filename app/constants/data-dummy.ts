export const userData: UserManagementProps[] = [
  {
    id: '0001',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '**************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0002',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0003',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '02 Jan, 2025'
  },
  {
    id: '0004',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0005',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0006',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0007',
    name: '<PERSON><PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0008',
    name: 'Floyd Ullrich',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0009',
    name: 'Drew Hettinger',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0010',
    name: 'Christian Blick MD',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0011',
    name: 'Ryan Moen',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0012',
    name: 'Brent Morar I',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0013',
    name: 'Christy Bins',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0014',
    name: 'Marsha Monahan',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0015',
    name: 'Curtis Breitenberg',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0016',
    name: 'Lori Nader',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0017',
    name: 'Tomasa Stiedemann',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '03 Jan, 2025'
  },
  {
    id: '0018',
    name: 'Jerrod Mohr',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0019',
    name: 'Reina Tillman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0020',
    name: 'Allan Schuppe',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0021',
    name: 'Irving Bosco',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '04 Jan, 2025'
  },
  {
    id: '0022',
    name: 'Lia Schulist',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0023',
    name: 'Wilfredo Cummerata',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0024',
    name: 'Domenic Hilpert',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0025',
    name: 'Clifton Ledner',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0026',
    name: 'Alene Champlin',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0027',
    name: 'Junior Bechtelar',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '05 Jan, 2025'
  },
  {
    id: '0028',
    name: "Wilma O'Hara",
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0029',
    name: 'Matilde Zieme',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0030',
    name: 'Kiara Gulgowski',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0031',
    name: 'Dewey Smitham',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0032',
    name: 'Stuart Hermann',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '06 Jan, 2025'
  },
  {
    id: '0033',
    name: 'Elsa Lockman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0034',
    name: 'Leola Rogahn',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0035',
    name: 'Darrick Ritchie',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0036',
    name: 'Ora Leffler',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0037',
    name: 'Toni Lakin',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0038',
    name: "Alfredo O'Keefe",
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '07 Jan, 2025'
  },
  {
    id: '0039',
    name: 'Traci Kuhic',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0040',
    name: 'Nickolas Barton',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0041',
    name: 'Jaqueline Emard',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0042',
    name: 'Margarito Waelchi',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0043',
    name: 'Mariano Feest',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0044',
    name: 'Luann Collins',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0045',
    name: 'Francisca Emmerich',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '08 Jan, 2025'
  },
  {
    id: '0046',
    name: 'Lenny Bernier',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0047',
    name: 'Judy Kautzer',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0048',
    name: 'Rosalva Doyle',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0049',
    name: 'Edna Rice',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0050',
    name: 'Rufus Johns',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  }
];
export const categoryData: CategoryProps[] = [
  {
    id: 'cat_001',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
    name: 'Electronics',
    status: 'Active',
    date: new Date('2024-01-15T10:00:00Z')
  },
  {
    id: 'cat_002',
    image: 'https://randomuser.me/api/portraits/men/2.jpg',
    name: 'Books',
    status: 'Active',
    date: new Date('2023-11-20T14:30:00Z')
  },
  {
    id: 'cat_003',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    name: 'Clothing',
    status: 'Inactive',
    date: new Date('2024-03-01T09:15:00Z')
  },
  {
    id: 'cat_004',
    image: 'https://randomuser.me/api/portraits/men/4.jpg',
    name: 'Home Goods',
    status: 'Active',
    date: new Date('2023-09-10T11:00:00Z')
  },
  {
    id: 'cat_005',
    image: 'https://randomuser.me/api/portraits/men/5.jpg',
    name: 'Sports & Outdoors',
    status: 'Active',
    date: new Date('2024-02-28T16:00:00Z')
  },
  {
    id: 'cat_006',
    image: 'https://randomuser.me/api/portraits/men/6.jpg',
    name: 'Toys & Games',
    status: 'Inactive',
    date: new Date('2024-04-05T10:30:00Z')
  },
  {
    id: 'cat_007',
    image: 'https://randomuser.me/api/portraits/men/7.jpg',
    name: 'Food & Groceries',
    status: 'Active',
    date: new Date('2023-10-25T08:45:00Z')
  },
  {
    id: 'cat_008',
    image: 'https://randomuser.me/api/portraits/men/8.jpg',
    name: 'Automotive',
    status: 'Active',
    date: new Date('2024-01-01T13:00:00Z')
  },
  {
    id: 'cat_009',
    image: 'https://randomuser.me/api/portraits/men/9.jpg',
    name: 'Health & Beauty',
    status: 'Active',
    date: new Date('2024-05-18T15:00:00Z')
  },
  {
    id: 'cat_010',
    image: 'https://randomuser.me/api/portraits/men/10.jpg',
    name: 'Garden & Patio',
    status: 'Inactive',
    date: new Date('2023-07-01T09:00:00Z')
  },
  {
    id: 'cat_011',
    image: 'https://randomuser.me/api/portraits/men/11.jpg',
    name: 'Jewelry',
    status: 'Active',
    date: new Date('2024-06-20T10:00:00Z')
  },
  {
    id: 'cat_012',
    image: 'https://randomuser.me/api/portraits/men/12.jpg',
    name: 'Pet Supplies',
    status: 'Active',
    date: new Date('2023-12-12T17:00:00Z')
  },
  {
    id: 'cat_013',
    image: 'https://randomuser.me/api/portraits/men/13.jpg',
    name: 'Arts & Crafts',
    status: 'Active',
    date: new Date('2024-07-01T11:00:00Z')
  },
  {
    id: 'cat_014',
    image: 'https://randomuser.me/api/portraits/men/14.jpg',
    name: 'Baby Products',
    status: 'Inactive',
    date: new Date('2023-08-01T14:00:00Z')
  },
  {
    id: 'cat_015',
    image: 'https://randomuser.me/api/portraits/men/15.jpg',
    name: 'Music & Instruments',
    status: 'Active',
    date: new Date('2024-02-14T09:00:00Z')
  },
  {
    id: 'cat_016',
    image: 'https://randomuser.me/api/portraits/men/16.jpg',
    name: 'Software',
    status: 'Active',
    date: new Date('2024-05-05T12:00:00Z')
  },
  {
    id: 'cat_017',
    image: 'https://randomuser.me/api/portraits/men/17.jpg',
    name: 'Travel Gear',
    status: 'Inactive',
    date: new Date('2023-10-01T10:00:00Z')
  }
];
export const categoryAddOnRecords: CategoryAddOnProps[] = [
  {
    id: 'ca001',
    image: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
    name: 'Sauces',
    status: 'Active',
    variants: [
      {
        id: 'v001',
        image: 'https://randomuser.me/api/portraits/thumb/women/2.jpg',
        name: 'Ketchup',
        price: '0.50'
      },
      {
        id: 'v002',
        image: 'https://randomuser.me/api/portraits/thumb/men/3.jpg',
        name: 'Mustard',
        price: '0.50'
      },
      {
        id: 'v003',
        image: 'https://randomuser.me/api/portraits/thumb/women/4.jpg',
        name: 'BBQ Sauce',
        price: '0.75'
      }
    ],
    date: new Date('2024-01-15T10:00:00Z')
  },
  {
    id: 'ca002',
    image: 'https://randomuser.me/api/portraits/thumb/women/5.jpg',
    name: 'Drinks',
    status: 'Active',
    variants: [
      {
        id: 'v004',
        image: 'https://randomuser.me/api/portraits/thumb/men/6.jpg',
        name: 'Coca-Cola',
        price: '2.00'
      },
      {
        id: 'v005',
        image: 'https://randomuser.me/api/portraits/thumb/women/7.jpg',
        name: 'Sprite',
        price: '2.00'
      },
      {
        id: 'v006',
        image: 'https://randomuser.me/api/portraits/thumb/men/8.jpg',
        name: 'Bottled Water',
        price: '1.50'
      }
    ],
    date: new Date('2024-02-20T11:30:00Z')
  },
  {
    id: 'ca003',
    image: 'https://randomuser.me/api/portraits/thumb/men/9.jpg',
    name: 'Cheese Options',
    status: 'Active',
    variants: [
      {
        id: 'v007',
        image: 'https://randomuser.me/api/portraits/thumb/women/10.jpg',
        name: 'Cheddar Slice',
        price: '1.00'
      },
      {
        id: 'v008',
        image: 'https://randomuser.me/api/portraits/thumb/men/11.jpg',
        name: 'Mozzarella Shreds',
        price: '1.25'
      }
    ],
    date: new Date('2024-03-01T09:15:00Z')
  },
  {
    id: 'ca004',
    image: 'https://randomuser.me/api/portraits/thumb/women/12.jpg',
    name: 'Pizza Toppings',
    status: 'Active',
    variants: [
      {
        id: 'v009',
        image: 'https://randomuser.me/api/portraits/thumb/men/13.jpg',
        name: 'Pepperoni',
        price: '1.50'
      },
      {
        id: 'v010',
        image: 'https://randomuser.me/api/portraits/thumb/women/14.jpg',
        name: 'Mushrooms',
        price: '1.00'
      },
      {
        id: 'v011',
        image: 'https://randomuser.me/api/portraits/thumb/men/15.jpg',
        name: 'Black Olives',
        price: '0.75'
      },
      {
        id: 'v012',
        image: 'https://randomuser.me/api/portraits/thumb/women/16.jpg',
        name: 'Red Onions',
        price: '0.75'
      }
    ],
    date: new Date('2024-04-10T14:00:00Z')
  },
  {
    id: 'ca005',
    image: 'https://randomuser.me/api/portraits/thumb/men/17.jpg',
    name: 'Side Dishes',
    status: 'Active',
    variants: [
      {
        id: 'v013',
        image: 'https://randomuser.me/api/portraits/thumb/women/18.jpg',
        name: 'French Fries',
        price: '3.00'
      },
      {
        id: 'v014',
        image: 'https://randomuser.me/api/portraits/thumb/men/19.jpg',
        name: 'Side Salad',
        price: '3.50'
      }
    ],
    date: new Date('2024-05-05T16:45:00Z')
  },
  {
    id: 'ca006',
    image: 'https://randomuser.me/api/portraits/thumb/women/20.jpg',
    name: 'Desserts',
    status: 'Inactive',
    variants: [
      {
        id: 'v015',
        image: 'https://randomuser.me/api/portraits/thumb/men/21.jpg',
        name: 'Chocolate Brownie',
        price: '4.00'
      },
      {
        id: 'v016',
        image: 'https://randomuser.me/api/portraits/thumb/women/22.jpg',
        name: 'Vanilla Ice Cream',
        price: '3.00'
      }
    ],
    date: new Date('2024-06-12T08:00:00Z')
  },
  {
    id: 'ca007',
    image: 'https://randomuser.me/api/portraits/thumb/men/23.jpg',
    name: 'Bread Choices',
    status: 'Active',
    variants: [
      {
        id: 'v017',
        image: 'https://randomuser.me/api/portraits/thumb/women/24.jpg',
        name: 'White Bread',
        price: '0.75'
      },
      {
        id: 'v018',
        image: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
        name: 'Wheat Bread',
        price: '0.85'
      }
    ],
    date: new Date('2024-07-25T13:00:00Z')
  }
];
export const productData: ProductProps[] = [
  {
    id: 'prod-1',
    name: 'Condo & Apartment',
    category: [categoryData[0], categoryData[2], categoryData[3]], // Electronics
    status: 'Active',
    date: new Date('2024-06-01T09:00:00Z')
  },
  {
    id: 'prod-2',
    name: 'Flat & Shop House',
    category: [categoryData[1]], // Fashion
    status: 'Active',
    date: new Date('2024-06-05T14:00:00Z')
  },
  {
    id: 'prod-3',
    name: 'Villa',
    category: [categoryData[2]], // Home & Garden
    status: 'Active',
    date: new Date('2024-06-10T10:30:00Z')
  },
  {
    id: 'prod-4',
    name: 'General Cleaning',
    category: [categoryData[3]], // Books
    status: 'Active',
    date: new Date('2024-06-15T11:00:00Z')
  },
  {
    id: 'prod-5',
    name: 'Deep Cleaning',
    category: [categoryData[4]], // Sports & Outdoors (Inactive category, perhaps still visible but unpurchasable)
    status: 'Inactive',
    date: new Date('2024-06-20T13:00:00Z')
  }
];
export const productOptions: ProductOptionProps[] = [
  {
    id: '1',
    name: 'Basic Plan',
    price: '9.99',
    status: 'Active',
    date: new Date('2025-01-01')
  },
  {
    id: '2',
    name: 'Standard Plan',
    price: '19.99',
    status: 'Active',
    date: new Date('2025-01-10')
  },
  {
    id: '3',
    name: 'Premium Plan',
    price: '29.99',
    status: 'Inactive',
    date: new Date('2025-01-15')
  },
  {
    id: '4',
    name: 'Enterprise Plan',
    price: '49.99',
    status: 'Active',
    date: new Date('2025-02-01')
  },
  {
    id: '5',
    name: 'Trial Pack',
    price: '0.00',
    status: 'Active',
    date: new Date('2025-02-05')
  },
  {
    id: '6',
    name: 'Student Plan',
    price: '5.99',
    status: 'Inactive',
    date: new Date('2025-02-10')
  },
  {
    id: '7',
    name: 'Business Plan',
    price: '39.99',
    status: 'Active',
    date: new Date('2025-02-15')
  },
  {
    id: '8',
    name: 'Starter Kit',
    price: '14.99',
    status: 'Active',
    date: new Date('2025-03-01')
  },
  {
    id: '9',
    name: 'Growth Plan',
    price: '24.99',
    status: 'Inactive',
    date: new Date('2025-03-05')
  },
  {
    id: '10',
    name: 'Marketing Add-on',
    price: '4.99',
    status: 'Active',
    date: new Date('2025-03-10')
  },
  {
    id: '11',
    name: 'Analytics Add-on',
    price: '3.99',
    status: 'Inactive',
    date: new Date('2025-03-12')
  },
  {
    id: '12',
    name: 'AI Feature Pack',
    price: '12.00',
    status: 'Active',
    date: new Date('2025-03-15')
  },
  {
    id: '13',
    name: 'Security Suite',
    price: '7.50',
    status: 'Active',
    date: new Date('2025-03-20')
  },
  {
    id: '14',
    name: 'Data Export',
    price: '2.00',
    status: 'Inactive',
    date: new Date('2025-03-25')
  },
  {
    id: '15',
    name: 'Support Package',
    price: '6.00',
    status: 'Active',
    date: new Date('2025-04-01')
  },
  {
    id: '16',
    name: 'Cloud Storage 100GB',
    price: '8.00',
    status: 'Active',
    date: new Date('2025-04-05')
  },
  {
    id: '17',
    name: 'Cloud Storage 1TB',
    price: '15.00',
    status: 'Inactive',
    date: new Date('2025-04-10')
  },
  {
    id: '18',
    name: 'Video Conferencing',
    price: '10.00',
    status: 'Active',
    date: new Date('2025-04-15')
  },
  {
    id: '19',
    name: 'API Access',
    price: '20.00',
    status: 'Active',
    date: new Date('2025-04-20')
  },
  {
    id: '20',
    name: 'Team Collaboration',
    price: '11.00',
    status: 'Inactive',
    date: new Date('2025-04-25')
  },
  {
    id: '21',
    name: 'Onboarding Service',
    price: '30.00',
    status: 'Active',
    date: new Date('2025-05-01')
  },
  {
    id: '22',
    name: 'Customization Pack',
    price: '25.00',
    status: 'Inactive',
    date: new Date('2025-05-05')
  }
];
export const dummyOrders: OrderComponentProps[] = [
  {
    id: '#100001',
    services: 2,
    date: '5 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
  },
  {
    id: '#100002',
    services: 3,
    date: '6 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
  },
  {
    id: '#100003',
    services: 5,
    date: '7 Aug, 2025',
    status: 'Completed',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
  },
  {
    id: '#100004',
    services: 1,
    date: '8 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
  },
  {
    id: '#100005',
    services: 4,
    date: '9 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
  },
];

export interface OrderDetail {
  name: string;
  address: string;
}

export const orderDetails: Record<string, OrderDetail> = {
  '#100003': {
    name: 'John Doe',
    address: '123 Main Street, City A',
  },
  '#100004': {
    name: 'Jane Smith',
    address: '456 Oak Avenue, City B',
  },
  '#100005': {
    name: 'Alice Johnson',
    address: '789 Pine Road, City C',
  },
  '#100001': {
    name: 'Alice Johnson',
    address: '789 Pine Road, City C',
  },
  '#100002': {
    name: 'Alice Johnson',
    address: '789 Pine Road, City C',
  },
};

export const serviceDetailsDummy: ServiceDetailsProps= {
  id: 'svc-001',
  serviceName: 'Service 1',
  category: 'Home Cleaning',
  addOns: 1,
  price: 100.0,
  imageSrc: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
  items: [
    { id: '1', name: 'Bathroom', checked: false },
  { id: '2', name: 'Living Room', checked: false },
  { id: '3', name: 'Kitchen', checked: false },
  { id: '4', name: 'Storage Room', checked: false },
  { id: '5', name: 'Swimming Pool', checked: false },
  { id: '6', name: 'Balcony', checked: false },
  { id: '7', name: 'Lamp or Light', checked: false },
  { id: '8', name: 'Floor', checked: false },
  { id: '9', name: 'Aircon', checked: false },
  { id: '10', name: 'Sofa', checked: false },
  { id: '11', name: 'Cabinet', checked: false },
  { id: '12', name: 'Fridge', checked: false },
  { id: '13', name: 'Curtain', checked: false },
  { id: '14', name: 'Elevator', checked: false },
  { id: '15', name: 'Carpet', checked: false },
  { id: '16', name: 'Laundry', checked: false },
  { id: '17', name: 'Cobwebs', checked: false },
  { id: '18', name: 'Mattress', checked: false },
  { id: '19', name: 'Windows', checked: false },
  { id: '20', name: 'Wall', checked: false },
  { id: '21', name: 'Ceiling', checked: false },
  { id: '22', name: 'Stair', checked: false },
  { id: '23', name: 'Mirror', checked: false },
  { id: '24', name: 'Roof', checked: false }
  ] as ServiceItem[]
};
