// import React from 'react';
// import { Button } from '@/components/ui/button';
// import { useAuth } from '@/hook/useAuth';

// const Home: React.FC = () => {
//   const { user, logout } = useAuth(); // Access user info from context

//   return (
//     <div>
//       <h1>Home</h1>
//       {user ? (
//         <p>Welcome to your protected Home, {user.username}!</p>
//       ) : (
//         <p>You should be logged in to see this.</p> // This message should ideally not be seen due to loader
//       )}
//       <p>This page requires authentication to access.</p>
//       <div className="flex flex-row">
//         <Button onClick={() => logout()}>logout</Button>
//       </div>
//     </div>
//   );
// };

// export default Home;
