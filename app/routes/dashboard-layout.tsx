import { AppSidebar } from '@/components/common/app-sidebar';
import DashboardHeader from '@/components/common/dashboard-header';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Outlet } from 'react-router';

export default function DashboardLayout() {
  return (
    <div className="h-screen flex">
      <SidebarProvider>
        <AppSidebar />
        <main className="flex flex-1 flex-col bg-background overflow-hidden">
          <div className="shrink-0">
            <DashboardHeader />
          </div>
          <div className="flex-1 overflow-auto">
            <Outlet />
          </div>
        </main>
      </SidebarProvider>
    </div>
  );
}
