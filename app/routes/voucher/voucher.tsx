import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router';

export default function Voucher() {
  const navigate = useNavigate();

  const handleNewVoucher = () => {
    navigate('/voucher/new-voucher');
  };

  const handleVoucherDetails = () => {
    navigate('/voucher/123');
  };
  return (
    <div>
      <Button onClick={handleNewVoucher}>new voucher</Button>
      <Button onClick={handleVoucherDetails}>edit voucher id 123</Button>
    </div>
  );
}
