import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import DraggableInputPanel from '@/components/common/draggable/draggable-input-panel';
import { useState } from 'react';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import {
  productOptionSchema,
  type ProductOptionSchemaProps
} from '@/lib/schema/product-option-schema';

export default function NewProductOption() {
  const { t } = useTranslation();
  const [tasks, setTasks] = useState<Task[]>([]);

  const form = useForm<ProductOptionSchemaProps>({
    mode: 'onSubmit',
    resolver: zod<PERSON>esolver(productOptionSchema),
    defaultValues: {
      title: '',
      price: '',
      cleaners: '',
      hours: '',
      bedroom: '',
      floor: ''
    }
  });

  const onSubmit = async (values: ProductOptionSchemaProps) => {
    console.log('Form submitted with values:', values);
    // Here you would typically handle the form submission, e.g., calling an API
  };

  return (
    <div>
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />
      <ContentWrapper>
        <div className="p-6 flex flex-row gap-6 items-baseline">
          <Card className="flex flex-1">
            <CardHeader className="gap-4">
              <CardTitle>{t('productPage.details')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('title')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('title')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('price')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('price')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cleaners"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('cleaners')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('cleaners')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('price')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('price')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="bedroom"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('bedroom')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('bedroom')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="floor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('floor')}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('floor')} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          <div className="w-[368px] flex flex-col gap-4 h-full">
            <DraggableInputPanel
              title={t('productPage.taskInformation')}
              data={tasks}
              onChange={setTasks}
            />
          </div>
        </div>
      </ContentWrapper>
    </div>
  );
}
