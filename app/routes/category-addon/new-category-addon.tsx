import { <PERSON>, CardContent, CardFooter, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import NewCategoryAddonCard from '@/components/category/new-category-addon-card';
import {
  dynamicCategoryAddonFormSchema,
  type DynamicCategoryAddonSchemaProps
} from '@/lib/schema/category-addon-schema';
import { Form } from '@/components/ui/form';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';

export default function NewCategoryAddon() {
  const { t } = useTranslation();
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  const form = useForm<DynamicCategoryAddonSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(dynamicCategoryAddonFormSchema),
    defaultValues: {
      categoryAddonVariants: [],
      categoryAddon: {
        id: '0',
        name: '',
        photo: '',
        price: ''
      }
    }
  });

  // useFieldArray hook to manage the dynamic 'items' array
  const { fields, remove, append, move } = useFieldArray({
    control: form.control,
    name: 'categoryAddonVariants' // This matches the 'items' array in your Zod schema
  });

  const onSubmit = async (values: DynamicCategoryAddonSchemaProps) => {
    console.log('Form submitted with values:', values);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex((field) => field.id === active.id);
      const newIndex = fields.findIndex((field) => field.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        // Use the 'move' function from useFieldArray
        move(oldIndex, newIndex);
      }
    }
  };

  const handleClearAll = () => {
    remove();
  };

  const handleAddMore = () => {
    append({ id: Date.now().toString(), name: '', photo: '', price: '' });
  };

  return (
    <div>
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />
      <ContentWrapper>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col h-full relative">
            <div className="flex-1 overflow-y-auto p-6">
              <Card className="mb-6">
                <CardHeader className="gap-4">
                  <CardTitle>{t('categoryPage.details')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <NewCategoryAddonCard
                    control={form.control}
                    nameName="categoryAddon.name"
                    photoName="categoryAddon.photo"
                    priceName="categoryAddon.price"
                  />
                </CardContent>
              </Card>
              <Card className="p-6">
                <CardHeader className="p-0">
                  <div className="flex justify-between">
                    <CardTitle>{t('categoryPage.productVariant')}</CardTitle>
                    {fields.length > 0 && (
                      <Button
                        className="text-destructive !pr-0 !pt-0"
                        variant="link"
                        type="button"
                        onClick={handleClearAll}
                      >
                        {t('categoryPage.clearAll')}
                      </Button>
                    )}
                  </div>
                </CardHeader>
                {fields.length > 0 && (
                  <CardContent className="p-0">
                    <DndContext
                      sensors={sensors}
                      collisionDetection={closestCenter}
                      onDragEnd={handleDragEnd}
                    >
                      <SortableContext
                        items={fields.map((task) => task.id)}
                        strategy={verticalListSortingStrategy}
                      >
                        <div className="flex flex-col gap-6 divide-accent divide-y-2">
                          {fields.map((field, index) => (
                            <div key={field.id} className="pb-6">
                              <NewCategoryAddonCard
                                id={field.id}
                                key={field.id}
                                title={`Variant ${index + 1}`}
                                nameName={`categoryAddonVariants.${index}.name`}
                                priceName={`categoryAddonVariants.${index}.price`}
                                photoName={`categoryAddonVariants.${index}.photo`}
                                control={form.control}
                                onDelete={() => {
                                  remove(index);
                                }}
                              />
                            </div>
                          ))}
                        </div>
                      </SortableContext>
                    </DndContext>
                  </CardContent>
                )}
                <CardFooter className="justify-end">
                  <Button
                    type="button"
                    variant="link"
                    className="!pr-0 !pb-0"
                    onClick={handleAddMore}
                  >
                    <Plus />
                    {t('categoryPage.addAnotherVariant')}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </form>
        </Form>
      </ContentWrapper>
    </div>
  );
}
