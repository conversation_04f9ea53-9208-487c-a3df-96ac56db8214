import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import ProfilePicker from '@/components/common/profile-picker';
import CustomSelect from '@/components/common/custom-select';
import DragDropFileUpload from '@/components/common/drag-drop-file-upload';
import { categorySchema, type CategorySchemaProps } from '@/lib/schema/category-schema';
import DraggableInputPanel from '@/components/common/draggable/draggable-input-panel';
import { useState } from 'react';
import DraggableComboboxPanel from '@/components/common/draggable/draggable-combobox-panel';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';

export default function NewCategory() {
  const { t } = useTranslation();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [products, setProducts] = useState<DraggableComboBoxProps[]>([]);
  const [categoryAddOn, setCategoryAddOn] = useState<DraggableComboBoxProps[]>([]);

  const form = useForm<CategorySchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: 'dara',
      status: 'Active',
      attachments: []
    }
  });

  const onSubmit = async (values: CategorySchemaProps) => {
    console.log('Form submitted with values:', values);
    // Here you would typically handle the form submission, e.g., calling an API
  };

  return (
    <div>
      <CustomHeader onSave={form.handleSubmit(onSubmit)} />
      <ContentWrapper>
        <div className="p-6 flex flex-row gap-6 items-baseline">
          <Card className="flex flex-1">
            <CardHeader className="gap-4">
              <CardTitle>{t('categoryPage.details')}</CardTitle>
              <ProfilePicker />
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-3">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>

                          <FormControl>
                            <Input placeholder="Name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Status</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder="Status"
                            data={[
                              { label: 'Active', value: 'Active' },
                              { label: 'Resigned', value: 'Resigned' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="mt-4">
                    <DragDropFileUpload placeholder="What's Included" />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          <div className="w-[368px] flex flex-col gap-4 h-full">
            <DraggableInputPanel
              title={t('categoryPage.taskInformation')}
              data={tasks}
              onChange={setTasks}
            />
            <DraggableComboboxPanel
              title={t('categoryPage.product')}
              buttonText={t('categoryPage.addProduct')}
              data={products}
              onChange={setProducts}
            />
            <DraggableComboboxPanel
              title={t('categoryPage.categoryAddOn')}
              buttonText={t('categoryPage.addCategoryAddOn')}
              data={categoryAddOn}
              onChange={setCategoryAddOn}
            />
          </div>
        </div>
      </ContentWrapper>
    </div>
  );
}
