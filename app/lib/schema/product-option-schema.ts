import { z } from 'zod';

export const productOptionSchema = z.object({
  title: z.string().min(1, 'Name is required'),
  price: z.string().min(1, 'Price is required'),
  cleaners: z.string().min(1, 'Cleaners is required'),
  hours: z.string().min(1, 'Hours is required'),
  bedroom: z.string().min(1, 'Bedroom is required'),
  floor: z.string().min(1, 'Floor is required')
});

export type ProductOptionSchemaProps = z.infer<typeof productOptionSchema>;
