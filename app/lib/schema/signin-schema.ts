import { string, z } from 'zod';

export const signInSchema = z.object({
  // email: string({ required_error: 'Email is required' })
  //   .min(1, 'Email is required')
  //   .email('Invalid email'),
  username: string({ required_error: 'Userna<PERSON> is required' }),
  password: string({ required_error: 'Password is required' })
    .min(1, 'Password is required')
    .min(8, 'Password must be more than 8 characters')
    .max(32, 'Password must be less than 32 characters')
});

export type SignInSchemaProps = z.infer<typeof signInSchema>;
