import { z } from 'zod';

export const categoryAddonSchema = z.object({
  id: z.string().optional(),
  // Photo field: optional, can be an empty string or a valid URL
  photo: z.string().url('Must be a valid URL for the photo.').or(z.literal('')).optional(),
  // Name field: required, minimum 1 character
  name: z.string().min(1, 'Name is required.'),
  // Price field: required, converted to number, minimum 0.01
  price: z.string().min(1, 'Price is required.')
  // price: z.preprocess(
  //   (val) => parseFloat(String(val)), // Ensure input value (string) is parsed as a float
  //   z.number().min(0.01, 'Price must be a positive number.')
  // )
});

export const dynamicCategoryAddonFormSchema = z.object({
  // The 'items' array must contain at least one item
  categoryAddon: categoryAddonSchema,
  categoryAddonVariants: z
    .array(categoryAddonSchema)
    .min(1, { message: 'You must have at least one product item.' })
});

export type CategoryAddonSchemaProps = z.infer<typeof categoryAddonSchema>;
export type DynamicCategoryAddonSchemaProps = z.infer<typeof dynamicCategoryAddonFormSchema>;
