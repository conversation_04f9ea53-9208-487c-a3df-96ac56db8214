import { useNavigate } from 'react-router';
import HeaderRight from '../common/header-right';
import useCanGoBack from '@/hooks/use-can-go-back';
import { ChevronLeft } from 'lucide-react';
import { Button } from '../ui/button';
import HeaderTitle from '../headers/header-title';

export default function DashboardHeader() {
  const { canGoBack, hiddenHeader } = useCanGoBack();
  const navigate = useNavigate();
  const handleBack = () => {
    navigate(-1);
  };

  if (hiddenHeader) return null;

  return (
    <div className="h-[88px] p-6 pl-4 flex w-full items-center flex-row">
      <div className="flex flex-1 flex-row gap-4 items-center">
        {canGoBack && (
          <Button variant="ghost" size="icon" onClick={handleBack} disabled={!canGoBack}>
            <ChevronLeft className="!size-6" />
          </Button>
        )}
        <HeaderTitle />
      </div>
      <HeaderRight />
    </div>
  );
}
