import * as React from 'react';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import clsx from 'clsx';

type Props = {
  data: { label: string; value: string }[];
  placeholder?: string;
  notFoundText?: string;
  onAddNew?: () => void;
  value: string;
  onSelect: (value: string) => void;
  className?: string;
};

export function Combobox({
  data,
  placeholder,
  onAddNew,
  onSelect,
  value,
  className,
  notFoundText = 'Not found'
}: Props) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={clsx('w-[200px] justify-between h-9', className)}
        >
          {value ? data.find((framework) => framework.value === value)?.label : placeholder}
          <ChevronsUpDown className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className={clsx('w-[200px] p-0', className)}>
        <Command>
          <CommandInput placeholder={placeholder} className="h-9" />
          <CommandList>
            <CommandEmpty className="p-0">
              <div className="p-4">{notFoundText}</div>
              {onAddNew && (
                <Button variant="ghost" className="w-full" onClick={onAddNew}>
                  <Plus />
                  Add new
                </Button>
              )}
            </CommandEmpty>
            <CommandGroup>
              {data.map((framework) => (
                <CommandItem
                  key={framework.value}
                  value={framework.value}
                  onSelect={(currentValue) => {
                    onSelect(currentValue === value ? '' : currentValue);
                    setOpen(false);
                  }}
                >
                  {framework.label}
                  <Check
                    className={cn(
                      'ml-auto',
                      value === framework.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
