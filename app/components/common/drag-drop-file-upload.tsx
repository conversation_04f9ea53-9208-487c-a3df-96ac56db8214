import clsx from 'clsx';
import { X, Upload } from 'lucide-react';
import { useState, useRef, type ChangeEvent, type DragEvent } from 'react';

type Props = {
  placeholder?: string;
};

export default function DragDropFileUpload({ placeholder }: Props) {
  const [imgs, setImgs] = useState<string[]>([]);

  return (
    <div className="flex flex-col gap-2">
      <span>{placeholder}</span>
      {imgs.length > 0 && (
        <div className="grid grid-cols-5 gap-4 mb-8">
          {imgs.map((src, index) => (
            <Image
              key={index}
              src={src}
              onRemove={() => {
                setImgs(imgs.filter((_, i) => i !== index));
              }}
            />
          ))}
        </div>
      )}
      <Uploader onUploaded={(urls) => setImgs([...imgs, ...urls])} />
    </div>
  );
}

const Image = ({ src, onRemove }: { src: string; onRemove: () => void }) => {
  return (
    <div className="relative bg-gray-100 rounded-lg overflow-hidden shadow-sm aspect-square">
      <img src={src} alt={src} className="object-cover w-full h-full rounded-sm" />
      <div
        className="absolute top-2 right-2 cursor-pointer z-10 bg-white/30 rounded-full size-8 items-center flex justify-center"
        onClick={onRemove}
      >
        <X className="text-destructive" />
      </div>
    </div>
  );
};

const Uploader = ({ onUploaded }: { onUploaded: (url: string[]) => void }) => {
  const [dragging, setDragging] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(true);
  };

  const handleDragLeave = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    // No change in dragging state here, just prevent default to allow drop
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  };

  const handleFiles = (files: FileList): void => {
    const urls: string[] = [];
    Array.from(files).forEach((file: File) => {
      if (file.size > 10 * 1024 * 1024) {
        // Check for 10MB limit
        alert(`File ${file.name} exceeds the 10MB limit.`);
        return;
      }
      if (
        ![
          'image/jpeg',
          'image/png'
          // 'application/pdf'
        ].includes(file.type)
      ) {
        alert(`File ${file.name} is not a JPG, PNG.`);
        return;
      }

      // Add your upload logic here
      console.log('Selected file:', file.name, 'Type:', file.type, 'Size:', file.size);

      // Display preview for images
      if (file.type.startsWith('image/')) {
        urls.push(URL.createObjectURL(file));
      }
    });

    onUploaded(urls);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = (): void => {
    fileInputRef.current?.click();
  };

  return (
    <div
      className={clsx(
        'border border-dashed rounded-lg text-center cursor-pointer min-h-[192px] flex flex-col justify-center items-center transition-all duration-200 ease-in-out',
        {
          'border-blue-500 bg-blue-50': dragging,
          'border-gray-300 bg-gray-50': !dragging
        }
      )}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onClick={handleClick}
    >
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileInputChange}
        accept=".jpg,.jpeg,.png,.pdf"
        multiple
      />
      <div className="mb-2">
        <Upload className="text-gray-400" />
      </div>
      <p className="text-red-600 my-1">Select a file or drag and drop here</p>
      <p className="text-gray-600 text-sm">JPG or PNG, file size no more than 10MB</p>
    </div>
  );
};
