import useAuthStore from '@/store/auth-store';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { RightSideDrawer } from './notification-drawer';

export default function HeaderRight() {
  const { user } = useAuthStore();

  return (
    <div className="flex flex-row items-center gap-6">
      <RightSideDrawer />
      <div className="w-[2px] h-10 bg-border" />
      <div className="flex flex-row gap-3 justify-center items-center">
        <Avatar className="rounded-sm size-10">
          <AvatarImage src={user?.profileUrl} alt="profile-picture" />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-semibold">{user?.username}</p>
          <p>{user?.type}</p>
        </div>
      </div>
    </div>
  );
}
