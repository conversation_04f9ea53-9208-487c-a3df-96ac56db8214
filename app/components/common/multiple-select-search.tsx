'use client';

import { useState } from 'react';
import { Check, ChevronDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

type Props = {
  placeholder: string;
  data: { value: string; label: string }[];
  value: string[];
  onValueChange: (value: string[]) => void;
};

export default function MultipleSelectSearch({ data, placeholder, value, onValueChange }: Props) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const filteredOptions = data.filter((item) =>
    item.label.toLowerCase().includes(searchValue.toLowerCase())
  );

  const handleSelect = (val: string) => {
    if (value.includes(val)) {
      const temp = value.filter((item) => item !== val);
      onValueChange(temp);
    } else {
      onValueChange([...value, val]);
    }
  };

  const handleRemove = (val: string) => {
    const temp = value.filter((item) => item !== val);
    onValueChange(temp);
  };

  const selectedLabels = value
    .map((value) => data.find((item) => item.value === value)?.label)
    .filter(Boolean);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between h-10 bg-transparent hover:bg-transparent"
        >
          <div className="flex gap-2 flex-1 overflow-x-auto no-scrollbar">
            {value.length === 0 ? (
              <span className="text-muted-foreground whitespace-nowrap">{placeholder}</span>
            ) : (
              selectedLabels.map((label, index) => (
                <Badge
                  key={value[index]}
                  variant="secondary"
                  className="text-xs whitespace-nowrap flex-shrink-0 text-black bg-muted"
                >
                  {label}
                  <button
                    className="ml-1 rounded-full text-destructive"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleRemove(value[index]);
                    }}
                  >
                    <X className="size-4" />
                  </button>
                </Badge>
              ))
            )}
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>

      <PopoverContent className="w-full p-0" align="start">
        <div className="p-2">
          <Input
            placeholder="Search"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="h-9"
          />
        </div>

        <div className="max-h-60 overflow-auto">
          {filteredOptions.length === 0 ? (
            <div className="py-6 text-center text-sm text-muted-foreground">
              No technologies found.
            </div>
          ) : (
            filteredOptions.map((item) => (
              <div
                key={item.value}
                className={cn(
                  'flex items-center px-2 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground',
                  value.includes(item.value) && 'bg-accent'
                )}
                onClick={() => handleSelect(item.value)}
              >
                <Check
                  className={cn(
                    'mr-2 h-4 w-4',
                    value.includes(item.value) ? 'opacity-100' : 'opacity-0'
                  )}
                />
                {item.label}
              </div>
            ))
          )}
        </div>

        {value.length > 0 && (
          <div className="border-t p-1">
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-xs text-destructive"
              onClick={() => onValueChange([])}
            >
              Clear all ({value.length})
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
