import React from 'react';

type OrderStatus = 'Pending' | 'In Progress' | 'Completed';

interface OrderComponentProps {
  id?: string;
  services?: number;
  date?: string;
  status?: OrderStatus;
  imageSrc: string;
  isActive?: boolean;
  onClick?: (orderId?: string) => void;
}

const OrderComponent: React.FC<OrderComponentProps> = (props) => {
  const getStatusColor = (status?: OrderStatus) => {
    switch (status) {
      case 'Pending':
        return 'text-[12px] font-semibold bg-[#FEF7E9] text-[#F6B024]';
      case 'Completed':
        return 'text-[12px] font-semibold bg-[#E6F9F1] text-[#06C270]';
      case 'In Progress':
        return 'text-[12px] font-semibold bg-[#E8F0F7] text-[#1964AD]';
      default:
        return 'text-[12px] font-semibold bg-gray-100 text-gray-600';
    }
  };

  return (
    <div
      onClick={() => props.onClick?.(props.id)}
      className={`w-[352px] h-[74px] flex items-center justify-between p-[12px] bg-white 
        rounded-[8px] cursor-pointer
        ${props.isActive ? 'border-2 border-[#1964AD]' : 'border-2 border-transparent'}`}
    >
      <div className="flex items-center space-x-4">
        <img
          src={props.imageSrc}
          alt={`Order ${props.id || ''} icon`}
          className="w-[48px] h-[48px] object-cover rounded-full"
        />

        <div className="flex flex-col gap-1">
          <h3 className="text-[14px] font-bold text-[#1A1A1A]">
            Order ID {props.id || 'N/A'}
          </h3>
          <p className="text-[14px] font-medium text-[#707070]">
            {props.services !== undefined ? `${props.services} Services` : 'No service info'}
          </p>
        </div>
      </div>

      <div className="flex flex-col items-end gap-1">
        <span
          className={`px-2 py-1 rounded-full text-sm font-medium ${getStatusColor(
            props.status
          )}`}
        >
          {props.status || 'Unknown'}
        </span>
        {props.date && (
          <span className="text-[14px] font-medium text-[#707070]">{props.date}</span>
        )}
      </div>
    </div>
  );
};

export default OrderComponent;
