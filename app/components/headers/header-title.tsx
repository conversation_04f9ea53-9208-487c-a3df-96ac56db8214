import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router';

const getPageTitle = (path: string): string => {
  if (DASHBOARD_TITLE[path]) return DASHBOARD_TITLE[path];
  // Dynamic match (e.g., /employees/resignation/123)
  for (const key of Object.keys(DASHBOARD_TITLE)) {
    if (key.includes(':')) {
      // Convert '/employees/resignation/:id' to a regex like ^/employees/resignation/[^/]+$
      const regex = new RegExp('^' + key.replace(/:[^/]+/g, '[^/]+') + '$');
      if (regex.test(path)) return DASHBOARD_TITLE[key];
    }
  }
  return path;
};

const DASHBOARD_TITLE: DashboardTitleProps = {
  '/': 'header.dashboard',
  '/order': 'header.order',
  '/user': 'header.user',

  '/category': 'header.category',
  '/category/new': 'header.newCategory',

  '/category-addon': 'header.categoryAddon',
  '/category-addon/new': 'header.newCategoryAddon',

  '/product': 'header.product',
  '/product/new': 'header.newProduct',

  '/product-option': 'header.productOption',
  '/product-option/new': 'header.newProductOption',

  '/customer': 'header.customer',

  '/voucher': 'header.voucher',
  '/voucher/new-voucher': 'header.newVoucher',
  '/voucher/:id': 'header.editVoucher',

  '/banner': 'header.banner',
  '/service-bundle': 'header.serviceBundle',
  '/audit-log': 'header.auditLog',
  '/top-up': 'header.topUp',
  '/referral-program': 'header.referralProgram',
  '/notifications': 'header.notifications',
  '/setup': 'header.setup'
};

export default function HeaderTitle() {
  const location = useLocation();
  const { t } = useTranslation();

  useEffect(() => {}, []);
  return <p className="text-xl font-bold">{t(getPageTitle(location.pathname))}</p>;
}
