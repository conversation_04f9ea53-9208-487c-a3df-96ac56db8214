import type { Control } from 'react-hook-form';
import ProfilePicker from '../common/profile-picker';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';
import { useTranslation } from 'react-i18next';
import type { DynamicCategoryAddonSchemaProps } from '@/lib/schema/category-addon-schema';
import { CircleQuestionMark, Grip, Minus } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

type Props = {
  control: Control<DynamicCategoryAddonSchemaProps>;
  nameName: 'categoryAddon.name' | `categoryAddonVariants.${number}.name`;
  priceName: 'categoryAddon.price' | `categoryAddonVariants.${number}.price`;
  photoName: 'categoryAddon.photo' | `categoryAddonVariants.${number}.photo`;
  id?: string;
  title?: string;
  onDelete?: () => void;
};

export default function NewCategoryAddonCard({
  control,
  nameName,
  priceName,
  photoName,
  id,
  title,
  onDelete
}: Props) {
  const { t } = useTranslation();
  console.log({ photoName });
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: id || ''
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  };

  return (
    <div ref={setNodeRef} style={style}>
      {id && (
        <div className="flex flex-row mb-4 gap-3">
          <button
            className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
            type="button"
            {...attributes}
            {...listeners}
          >
            <Grip className="h-4 w-4" />
          </button>
          <div className="flex-1">{title}</div>
          <button type="button" onClick={onDelete} className="p-1 text-red-400">
            <Minus className="h-4 w-4" />
          </button>
        </div>
      )}

      <div className="grid grid-cols-3 gap-12">
        <ProfilePicker />
        <FormField
          control={control}
          name={nameName}
          render={({ field }) => (
            <FormItem className="flex flex-col items-start">
              <div className="flex flex-row w-full justify-between">
                <FormLabel>{t('name')}</FormLabel>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <CircleQuestionMark size={16} className="text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Tooltips content here</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <FormControl>
                <Input placeholder="Name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={priceName}
          render={({ field }) => (
            <FormItem className="flex flex-col items-start">
              <FormLabel>{t('price')}</FormLabel>
              <FormControl>
                <Input placeholder={t('price')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
