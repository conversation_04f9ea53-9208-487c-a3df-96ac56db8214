import '@tanstack/react-table'; // This line is crucial for module augmentation

declare module '@tanstack/react-table' {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData extends RowData, TValue> {
    // Add all your custom meta properties here
    isSticky?: boolean; // This is the new property you need
    className?: string;
    width?: number; // Optional width for the column
    stickyPosition?: number; // Optional position for sticky columns
  }
}
