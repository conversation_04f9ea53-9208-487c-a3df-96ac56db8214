type UserManagementProps = {
  id: string; // Corresponds to NO.
  name: string; // User's name
  role: string;
  phoneNumber: string;
  status: 'Active' | 'Deactivated' | '';
  date: string; // Date string (e.g., "01 Jan, 2025")
};

type CategoryProps = {
  id: string;
  image: string;
  name: string;
  status: 'Active' | 'Inactive';
  date: Date;
};

type DraggableProps = {
  id: string;
  isNew: boolean;
};

type Task = {
  title: string;
  isExpanded: boolean;
  description: string;
} & DraggableProps;

type DraggableComboBoxProps = {
  data: { label: string; value: string }[];
  value: string;
} & DraggableProps;

type VariantProps = {
  id: string;
  image: string;
  name: string;
  price: string;
};

type CategoryAddOnProps = {
  id: string;
  image: string;
  name: string;
  status: string;
  variants: VariantProps[];
  date: Date;
};

type ProductProps = {
  id: string;
  name: string;
  category: CategoryProps[];
  status: string;
  date: Date;
};

type ProductOptionProps = {
  id: string;
  name: string;
  price: string;
  status: string;
  date: Date;
};

type OrderComponentProps ={
  id: string;
  services: number;
  date: string;
  status: 'Pending' | 'In Progress' | 'Completed';
  imageSrc: string; // Image source URL
}

type ServiceDetailsProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  items: { id: string; name: string; checked: boolean }[];
  imageSrc: string;
};
type ServiceItem = {
  id: string;
  name: string;
  checked: boolean;
};