import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import { immer } from 'zustand/middleware/immer';

type AuthStoreState = {
  user: UserResponseProps | null;
  setUser: (user: UserResponseProps | null) => void;
};

const useAuthStore = create<AuthStoreState>()(
  persist(
    immer((set) => ({
      user: null,
      setUser: (user: UserResponseProps | null) => set({ user })
    })),
    {
      name: 'auth-store'
    }
  )
);

export default useAuthStore;
