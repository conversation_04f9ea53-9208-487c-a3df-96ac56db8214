import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import km from './km.json';
import en from './en.json';

export const localesInitializer = () => {
  i18next
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources: {
        en: {
          translation: en
        },
        km: {
          translation: km
        }
      },
      lng: 'en',
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false
      }
    });
};
