# 🚀 React Vite App

A modern React.js project bootstrapped with **Vite**, using:

- ⚛️ **React Router (Framework Mode)** – for powerful routing
- 🎨 **Tailwind CSS** – for utility-first styling
- 💎 **shadcn/ui** – for beautiful and accessible UI components
- ⚙️ **React Query** – for efficient data fetching and caching
- 📡 **Axios** – for HTTP requests
- 🧠 **Zustand** – for lightweight state management
- 🧹 **ESLint** – for linting
- 🧼 **Prettier** – for consistent code formatting

---

## 📦 Tech Stack

| Tech         | Description                                       |
| ------------ | ------------------------------------------------- |
| React + Vite | Fast dev server and build tool                    |
| Tailwind CSS | Utility-first CSS for rapid UI development        |
| Shadcn/UI    | Beautifully designed components built with Radix  |
| React Router | Declarative routing for React apps                |
| React Query  | Server-state management and caching               |
| Axios        | Promise-based HTTP client for browser and Node.js |
| Zustand      | Bear necessities for global state in React        |
| ESLint       | Linting for consistent code                       |
| Prettier     | Code formatter to enforce style consistency       |

---

## 🧑‍💻 Recommended VS Code Extensions

Make sure to install the following extensions in your IDE:

1. [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
2. [Prettier - Code formatter](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

---

## 🛠️ Getting Started

```bash
# 1. Clone the repository
git clone https://github.com/your-username/your-repo-name.git
cd your-repo-name

# 2. Install dependencies
yarn install

# 3. Setup environment variables
# Copy the content from .env.example into a new .env file
cp .env.example .env

# 4. Run prepare script (e.g. for husky setup)
yarn prepare

# 5. Start the development server
yarn dev
```
